import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ScrollNavigator from "@/components/ScrollNavigator";
import BackToHome from "@/components/BackToHome";
import agbMd from "@/data/agb.md?raw";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

const AGB = () => {
  const raw: string = agbMd;
  const sanitized = raw.replace(/^\s*###.*\n+/, "");

  // Split content into sections starting at lines beginning with "#### "
  const lines = sanitized.split(/\r?\n/);
  const sections: string[] = [];
  let buffer: string[] = [];
  for (const line of lines) {
    if (line.startsWith("#### ")) {
      if (buffer.length) sections.push(buffer.join("\n"));
      buffer = [line];
    } else {
      buffer.push(line);
    }
  }
  if (buffer.length) sections.push(buffer.join("\n"));

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        <div className="container mx-auto px-4 py-16 max-w-4xl">
          <BackToHome />
          <header className="mb-12 text-center">
            <h1 classN![1757784435756](image/AGB/1757784435756.png)![1757784446525](image/AGB/1757784446525.png)![1757784450717](image/AGB/1757784450717.png)![1757784465921](image/AGB/1757784465921.png)ame="text-4xl font-bold text-foreground mb-4">Allgemeine Geschäftsbedingungen</h1>
            <p className="text-lg text-muted-foreground">
              doorbit            </p>
          </header>

          <article className="prose prose-lg max-w-none">
            <div className="space-y-8 text-foreground">
              {sections.map((sec, idx) => (
                <section key={idx} className="mb-12 p-6 bg-card rounded-lg border">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      h3: ({...props}) => (<h2 className="text-2xl font-semibold mb-6 text-foreground" {...props} />),
                      h4: ({...props}) => (<h2 className="text-2xl font-semibold mb-6 text-foreground" {...props} />),
                      p: ({ children, ...props }) => {
                        const getText = (node: any): string => {
                          if (node == null) return "";
                          if (typeof node === "string" || typeof node === "number") return String(node);
                          if (Array.isArray(node)) return node.map(getText).join("");
                          if (node.props && node.props.children) return getText(node.props.children);
                          return "";
                        };
                        const text = getText(children);

                        // Teile Text bei nummerierten Sätzen auf
                        const parts = text.split(/(\([0-9]+\)[^(]*?)(?=\([0-9]+\)|$)/g).filter(Boolean);

                        if (parts.length > 1) {
                          // Mehrere nummerierte Teile gefunden
                          return (
                            <div {...props}>
                              {parts.map((part, index) => {
                                const trimmedPart = part.trim();
                                if (!trimmedPart) return null;
                                const isNumbered = /^\([0-9]+\)/.test(trimmedPart);
                                return (
                                  <p key={index} className={isNumbered ? 'mb-4' : ''}>
                                    {trimmedPart}
                                  </p>
                                );
                              })}
                            </div>
                          );
                        }

                        // Einzelner Absatz - normale Behandlung
                        const isNumberedPar = /^([>-]\s*|[\-–]\s*)?\(\d+\)/.test(text.trimStart());
                        const className = `${props.className ? props.className + ' ' : ''}${isNumberedPar ? 'mb-4' : ''}`;
                        return <p {...props} className={className}>{children}</p>;
                      },
                      ol: ({...props}) => (<ol className="ml-8 list-decimal space-y-6 mb-4" {...props} />),
                      blockquote: ({...props}) => (<blockquote className="not-prose m-0 w-full box-border pl-4 break-words break-all mb-4" {...props} />),
                      ul: ({...props}) => (<ul className="ml-8 list-disc space-y-2 mb-4" {...props} />),
                      li: ({...props}) => (<li className="pl-2" {...props} />),
                      code: ({...props}) => (<code className="break-words break-all whitespace-pre-wrap max-w-full" {...props} />),
                      pre: ({...props}) => (<pre className="break-words break-all whitespace-pre-wrap max-w-full" {...props} />),
                    }}
                  >
                    {sec}
                  </ReactMarkdown>
                </section>
              ))}
            </div>
          </article>
        </div>
      </main>
      <Footer />
      <ScrollNavigator />
    </div>
  );
};

export default AGB;

import Header from "@/components/Header";
import Hero from "@/components/Hero";
import Features from "@/components/Features";
import VideoCarousel from "@/components/VideoCarousel";
import Networks from "@/components/Networks";
import Pricing from "@/components/Pricing";
import Testimonials from "@/components/Testimonials";
import FAQ from "@/components/FAQ";
import CTA from "@/components/CTA";
import Footer from "@/components/Footer";
import ScrollNavigator from "@/components/ScrollNavigator";
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const Index = () => {
  const location = useLocation();

  useEffect(() => {
    // Handle hash navigation when component mounts
    if (location.hash) {
      const sectionId = location.hash.substring(1); // Remove the #
      setTimeout(() => {
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100); // Small delay to ensure DOM is ready
    }
  }, [location.hash]);

  return (
    <div className="min-h-screen scroll-smooth">
      <Header />
      <div id="hero">
        <Hero />
      </div>
      <div id="features">
        <Features />
      </div>
      <div id="videos">
        <VideoCarousel />
      </div>
      <div id="pricing">
        <Pricing />
      </div>
      <div id="testimonials">
        <Testimonials />
      </div>
      <div id="networks">
        <Networks />
      </div>
      <div id="faq">
        <FAQ />
      </div>
      <div id="cta">
        <CTA />
      </div>
      <Footer />
      <ScrollNavigator />
    </div>
  );
};

export default Index;

import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";

const BackToHome = () => {
  const { navigateToSection } = useNavigation();

  return (
    <Button
      variant="outline"
      onClick={() => navigateToSection('hero')}
      className="mb-6"
    >
      <ArrowLeft className="w-4 h-4 mr-2" />
      Zurück zur Startseite
    </Button>
  );
};

export default BackToHome;